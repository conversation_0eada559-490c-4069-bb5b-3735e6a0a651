<template>
  <div style="flex: 1; display: flex">
    <div v-if="loading" style="height: 100%; width: 100%; display: flex; justify-content: center; align-items: center">
      <a-spin></a-spin>
    </div>

    <div
      v-else-if="!!errorInfo"
      style="height: 100%; width: 100%; display: flex; justify-content: center; align-items: center"
    >
      <a-result status="error" :sub-title="errorInfo"></a-result>
    </div>

    <div v-else-if="!!resultData" style="height: 100%; width: 100%; display: flex">
      <!-- 左侧统计信息 -->
      <div class="left" style="width: 270px; padding: 14px; border-right: 1px solid #f0f0f0">
        <div class="item">
          <div class="label">方案编码</div>
          <div class="value">{{ resultData.caseCode }}</div>
        </div>
        <div class="item">
          <div class="label">方案名称</div>
          <div class="value">{{ resultData.caseName }}</div>
        </div>
        <div class="item">
          <div class="label">开始时间</div>
          <div class="value">{{ resultData.startTime }}</div>
        </div>
        <div class="item">
          <div class="label">结束时间</div>
          <div class="value">{{ resultData.endTime }}</div>
        </div>
        <div class="item">
          <div class="label">累计降雨量:</div>
          <div class="value">{{ resultData.totalRainfall }}mm</div>
        </div>
        <div class="item">
          <div class="label">起调水位:</div>
          <div class="value">{{ resultData.startWaterLevel }}m</div>
        </div>
        <div class="item">
          <div class="label">末期水位:</div>
          <div class="value">{{ resultData.endWaterLevel }}m</div>
        </div>
        <div class="item">
          <div class="label">最高水位</div>
          <div class="value">{{ resultData.maxWaterLevel }}m</div>
        </div>
        <div class="item">
          <div class="label">最低水位</div>
          <div class="value">{{ resultData.minWaterLevel }}m</div>
        </div>  
        <div class="item">
          <div class="label">累计入库水量:</div>
          <div class="value">{{ resultData.totalInflowVolume }}万m³</div>
        </div>
        <div class="item">
          <div class="label">累计出库水量:</div>
          <div class="value">{{ resultData.totalOutflowVolume }}万m³</div>
        </div>
        <div class="item">
          <div class="label">累计供水量:</div>
          <div class="value">{{ resultData.totalSupplyVolume }}万m³</div>
        </div>
        <div class="item">
          <div class="label">累计泄洪量:</div>
          <div class="value">{{ resultData.totalFloodVolume }}万m³</div>
        </div>
        <div class="item">
          <div class="label">洪峰流量:</div>
          <div class="value">{{ resultData.peakFlow }}m³/s</div>
        </div>
        <div class="item">
          <div class="label">峰现时间:</div>
          <div class="value">{{ resultData.peakTime }}</div>
        </div>
      </div>

      <!-- 右侧图表和表格 -->
      <div style="flex: 1; height: 100%">
        <div style="height: 50%">
          <BarAndLineMixChart :dataSource="chartData" />
        </div>

        <ResultTable :dataSource="resultData?.resvrDispResList || []" :resultData="resultData" style="height: 50%" />
      </div>
    </div>
  </div>
</template>

<script lang="jsx">
  import { getDispRes } from '../../services'
  // import { SocketClient } from '@/utils/sockClient.js'
  import BarAndLineMixChart from './BarAndLineMixChart.vue'
  import ResultTable from './ResultTable.vue'
  import moment from 'moment'

  export default {
    name: 'Result',
    props: ['baseInfo', 'outflowData'],
    components: {
      BarAndLineMixChart,
      ResultTable,
    },
    data() {
      return {
        loading: false,
        socketIns: null,
        errorInfo: null,
        resultData: null,
        chartData: [],
        pollingTimer: null, // 轮询定时器
        pollingCount: 0, // 轮询次数
        maxPollingCount: 30, // 最大轮询次数（5分钟，每10秒一次）
      }
    },
    computed: {},
    created() {
      this.loading = true
      this.$emit('update:isDisabledBtn', true)
      this.errorInfo = null

      // 检查是否有resvrDispId，如果有则调用真实接口
      if (this.outflowData && this.outflowData.resvrDispId) {
        this.loadModelResult()
      } else {
        // 没有resvrDispId时使用模拟数据
        setTimeout(() => {
          this.generateMockResult()
          this.$emit('update:isDisabledBtn', false)
          this.loading = false
        }, 2000)
      }
    },
    mounted() {},
    beforeDestroy() {
      // this.socketIns.disconnect()
      this.clearPolling()
    },
    methods: {
      // 加载真实的模型结果
      loadModelResult() {
        const params = {
          resvrDispId: this.outflowData.resvrDispId
        }

        getDispRes(params).then(res => {
          if (res.success && res.data) {
            // 检查模型状态
            if (res.data.modelStatus === 1) {
              // 模型执行中，开始轮询
              this.startPolling()
              return // 不设置loading为false，继续显示加载状态
            } else if (res.data.modelStatus === 9) {
              // 模型执行异常
              this.errorInfo = '模型执行异常: ' + (res.data.modelErr || '未知错误')
            } else if (res.data.modelStatus === 2) {
              // 模型执行成功
              this.processModelResult(res.data)
            } else {
              this.errorInfo = '未知的模型状态: ' + res.data.modelStatus
            }
          } else {
            this.errorInfo = '获取模型结果失败: ' + (res.message || '未知错误')
          }
          this.$emit('update:isDisabledBtn', false)
          this.loading = false
        }).catch(err => {
          console.error('调用模型结果接口失败:', err)
          this.errorInfo = '调用模型结果接口失败'
          this.$emit('update:isDisabledBtn', false)
          this.loading = false
        })
      },

      // 处理真实的模型结果数据
      processModelResult(data) {
        // 处理基本信息
        this.resultData = {
          caseCode: data.caseCode || '',
          caseName: data.caseName || '',
          startTime: data.startTime || '',
          endTime: data.endTime || '',
          startWaterLevel: data.startWaterLevel || 0,
          endWaterLevel: data.endWaterLevel || 0,
          maxWaterLevel: data.maxWaterLevel || 0,
          minWaterLevel: data.minWaterLevel || 0,
          totalRainfall: data.sumRainfall || 0,
          totalInflowVolume: data.sumInWater || 0,
          totalOutflowVolume: data.sumOutWater || 0,
          totalSupplyVolume: data.sumOutWater || 0, // 根据实际数据结构调整
          totalFloodVolume: data.sumFloodWater || 0,
          peakFlow: 0, // 需要从resvrDispResList中计算
          peakTime: '',
          resvrDispResList: data.resvrDispResList || [],
          // 来水信息
          inWater: data.inWater || {}
        }

        // 计算洪峰流量和峰现时间
        if (data.resvrDispResList && data.resvrDispResList.length > 0) {
          let maxInflow = 0
          let peakTime = ''

          data.resvrDispResList.forEach(item => {
            if (item.inflow > maxInflow) {
              maxInflow = item.inflow
              peakTime = item.tm
            }
          })

          this.resultData.peakFlow = maxInflow
          this.resultData.peakTime = peakTime
        }

        // 处理图表数据
        this.processChartData(data.resvrDispResList || [])
      },

      // 处理图表数据
      processChartData(resvrDispResList) {
        const rainData = {
          name: '时段雨量',
          data: resvrDispResList.map(el => [el.tm, el.rain || 0]),
        }

        const sumRainData = {
          name: '累计降雨量',
          data: rainData.data.map((el, idx) => {
            const sum = rainData.data.slice(0, idx + 1).reduce((a, b) => a + parseFloat(b[1] || 0), 0)
            return [el[0], +sum.toFixed(1)]
          }),
        }

        this.chartData = [
          rainData,
          sumRainData,
          {
            name: '水位',
            data: resvrDispResList.map(el => [el.tm, el.wlv || 0]),
          },
          {
            name: '入库流量',
            data: resvrDispResList.map(el => [el.tm, el.inflow || 0]),
          },
          {
            name: '供水流量',
            data: resvrDispResList.map(el => [el.tm, el.outflow || 0]), // 根据实际字段调整
          },
          {
            name: '泄洪流量',
            data: resvrDispResList.map(el => [el.tm, el.floodflow || 0]),
          },
        ]
      },

      generateMockResult() {
        // 生成模拟结果数据
        const startTime = moment(this.baseInfo.startTime)
        const endTime = moment(this.baseInfo.endTime)
        const duration = endTime.diff(startTime, 'hours')
        
        const resvrDispResList = []
        let totalRainfall = 0
        let totalInflowVolume = 0
        let totalSupplyVolume = 0
        let totalFloodVolume = 0
        let peakFlow = 0
        let peakTime = ''
        
        for (let i = 0; i <= duration; i++) {
          const time = startTime.clone().add(i, 'hours').format('YYYY-MM-DD HH:mm')
          // 使用从OutflowProcess传递过来的降雨数据，如果没有则使用随机数据
          const rain = this.outflowData?.rainfallData?.[i]?.rainfall || (Math.random() * 10) // 0-10mm
          const inflow = 100 + Math.random() * 50 // 100-150
          const supplyFlow = this.outflowData?.tableData?.[i]?.supplyFlow || (80 + Math.random() * 40)
          const floodFlow = this.outflowData?.tableData?.[i]?.floodFlow || (40 + Math.random() * 30)
          const waterLevel = 85 + Math.random() * 5 // 85-90
          
          totalRainfall += rain
          totalInflowVolume += inflow * 0.0036 // 转换为万m³
          totalSupplyVolume += supplyFlow * 0.0036
          totalFloodVolume += floodFlow * 0.0036
          
          if (inflow > peakFlow) {
            peakFlow = inflow
            peakTime = time
          }
          
          resvrDispResList.push({
            tm: time,
            rain: rain.toFixed(1),
            inflow: inflow.toFixed(1),
            supplyFlow: supplyFlow.toFixed(1),
            floodFlow: floodFlow.toFixed(1),
            wlv: waterLevel.toFixed(2),
            supplyVolume: (supplyFlow * 0.0036).toFixed(2),
            floodVolume: (floodFlow * 0.0036).toFixed(2),
          })
        }
        
        this.resultData = {
          caseCode: 'DISP_' + Date.now(),
          caseName: this.baseInfo.caseName,
          startTime: this.baseInfo.startTime,
          endTime: this.baseInfo.endTime,
          startWaterLevel: (85 + Math.random() * 2).toFixed(2),
          endWaterLevel: (87 + Math.random() * 2).toFixed(2),
          maxWaterLevel: (89 + Math.random() * 2).toFixed(2),
          minWaterLevel: (83 + Math.random() * 2).toFixed(2),
          totalRainfall: (this.outflowData?.totalRainfall || totalRainfall).toFixed(1),
          totalInflowVolume: totalInflowVolume.toFixed(1),
          totalOutflowVolume: (totalSupplyVolume + totalFloodVolume).toFixed(1),
          totalSupplyVolume: totalSupplyVolume.toFixed(1),
          totalFloodVolume: totalFloodVolume.toFixed(1),
          peakFlow: peakFlow.toFixed(1),
          peakTime,
          resvrDispResList,
        }
        
        // 处理图表数据
        const data = this.resultData.resvrDispResList
        
        const rainData = {
          name: '时段雨量',
          data: data.map(el => [el.tm, el.rain]),
        }
        const sumRainData = {
          name: '累计降雨量',
          data: rainData.data.map((el, idx) => {
            const sum = rainData.data.slice(0, idx + 1).reduce((a, b) => a + parseFloat(b[1]), 0)
            return [el[0], +sum.toFixed(1)]
          }),
        }
        this.chartData = [
          rainData,
          sumRainData,
          {
            name: '水位',
            data: data.map(el => [el.tm, el.wlv]),
          },
          {
            name: '入库流量',
            data: data.map(el => [el.tm, el.inflow]),
          },
          {
            name: '供水流量',
            data: data.map(el => [el.tm, el.supplyFlow]),
          },
          {
            name: '泄洪流量',
            data: data.map(el => [el.tm, el.floodFlow]),
          },
        ]
      },
      
      // 开始轮询
      startPolling() {
        this.pollingCount = 0
        this.pollingTimer = setInterval(() => {
          this.pollingCount++
          if (this.pollingCount > this.maxPollingCount) {
            this.clearPolling()
            this.errorInfo = '模型执行超时，请稍后手动刷新查看结果'
            this.$emit('update:isDisabledBtn', false)
            this.loading = false
            return
          }

          // 重新调用接口检查状态
          this.checkModelStatus()
        }, 10000) // 每10秒检查一次
      },

      // 清除轮询
      clearPolling() {
        if (this.pollingTimer) {
          clearInterval(this.pollingTimer)
          this.pollingTimer = null
        }
      },

      // 检查模型状态
      checkModelStatus() {
        const params = {
          resvrDispId: this.outflowData.resvrDispId
        }

        getDispRes(params).then(res => {
          if (res.success && res.data) {
            if (res.data.modelStatus === 2) {
              // 模型执行成功
              this.clearPolling()
              this.processModelResult(res.data)
              this.$emit('update:isDisabledBtn', false)
              this.loading = false
            } else if (res.data.modelStatus === 9) {
              // 模型执行异常
              this.clearPolling()
              this.errorInfo = '模型执行异常: ' + (res.data.modelErr || '未知错误')
              this.$emit('update:isDisabledBtn', false)
              this.loading = false
            }
            // 如果还是执行中状态(1)，继续轮询
          }
        }).catch(err => {
          console.error('轮询检查模型状态失败:', err)
          // 轮询失败不中断，继续下次轮询
        })
      },

      save() {
        // 传递resvrDispId用于保存
        const saveData = this.outflowData && this.outflowData.resvrDispId
          ? this.outflowData.resvrDispId
          : this.resultData.caseCode
        this.$emit('saveData', saveData)
      },
    },
  }
</script>

<style lang="less" scoped>
.left {
  .item {
    display: flex;
    height: 36px;
    line-height: 24px;
    align-items: center;
    
    .label {
      // width: 120px;
      color: #4e5969;
      // text-align: right;
      font-size: 13px;
    }
    .value {
      flex: 1;
      color: #1d2129;
      font-weight: 500;
      padding-left: 8px;
      // 文字溢出隐藏
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}
</style>
