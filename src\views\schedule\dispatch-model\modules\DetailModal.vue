<template>
  <ant-modal
    :visible="open"
    :modal-title="modalTitle"
    :loading="modalLoading"
    modalWidth="1370"
    @cancel="cancel"
    modalHeight="800"
  >
    <div slot="content" style="height: 100%; display: flex">
      <div class="left" v-if="!!resultData">
        <div class="item">
          <div class="label">调度方案编号:&nbsp;</div>
          <div class="value">{{ resultData.caseCode }}</div>
        </div>
        <div class="item">
          <div class="label">调度方案名称:&nbsp;</div>
          <div class="value">{{ resultData.caseName }}</div>
        </div>
        <!-- <div class="item" v-if="resultData.simulateType === 1">
          <div class="label">来水预报方案:&nbsp;</div>
          <div class="value">{{ waterFcstOptions.find(ele => ele.value == resultData.waterFcstId)?.label }}</div>
        </div> -->
        <div class="item">
          <div class="label">开始时间:&nbsp;</div>
          <div class="value">{{ resultData.startTime }}</div>
        </div>
        <div class="item">
          <div class="label">结束时间:&nbsp;</div>
          <div class="value">{{ resultData.endTime }}</div>
        </div>
        <div class="item">
          <div class="label">累计降雨量:&nbsp;</div>
          <div class="value">{{ resultData.inWater?.sumRain || resultData.sumRainfall || 0 }}mm</div>
        </div>
        <div class="item">
          <div class="label">起调水位:&nbsp;</div>
          <div class="value">{{ resultData.startWaterLevel }}m</div>
        </div>
        <div class="item">
          <div class="label">末期水位:&nbsp;</div>
          <div class="value">{{ resultData.endWaterLevel }}m</div>
        </div>
        <div class="item">
          <div class="label">最高水位:&nbsp;</div>
          <div class="value">{{ resultData.maxWaterLevel }}m</div>
        </div>
        <div class="item">
          <div class="label">最低水位:&nbsp;</div>
          <div class="value">{{ resultData.minWaterLevel }}m</div>
        </div>
        <div class="item">
          <div class="label">累计入库水量:&nbsp;</div>
          <div class="value">{{ resultData.inWater?.sumInWater || resultData.sumInWater || 0 }}万m³</div>
        </div>
        <div class="item">
          <div class="label">累计出库水量:&nbsp;</div>
          <div class="value">{{ resultData.sumOutWater || 0 }}万m³</div>
        </div>
        <div class="item">
          <div class="label">累计供水量:&nbsp;</div>
          <div class="value">{{ calculateSupplyWater() }}万m³</div>
        </div>
        <div class="item">
          <div class="label">累计泄洪量:&nbsp;</div>
          <div class="value">{{ resultData.sumFloodWater || 0 }}万m³</div>
        </div>
        <div class="item">
          <div class="label">洪峰流量:&nbsp;</div>
          <div class="value">{{ calculatePeakFlow() }}m³/s</div>
        </div>
        <div class="item">
          <div class="label">峰现时间:&nbsp;</div>
          <div class="value">{{ calculatePeakTime() }}</div>
        </div>
      </div>

      <div v-if="!!resultData" style="flex: 1; height: 100%">
        <div style="height: 50%">
          <BarAndLineMixChart :dataSource="chartData" />
        </div>

        <ResultTable :dataSource="resultData?.resvrDispResList || []" :resultData="resultData" style="height: 50%" />
      </div>
    </div>
    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import { getOptions } from '@/api/common'
  import { getDispRes } from '../services'
  import AntModal from '@/components/pt/dialog/AntModal'
  import moment from 'moment'
  import { getInWaterPage } from '../../incoming-water-model/services'
  import BarAndLineMixChart from './AddModal/BarAndLineMixChart.vue'
  import ResultTable from './AddModal/ResultTable.vue'

  export default {
    name: 'FormDrawer',
    props: ['simulateTypeOptions', 'sceneOptions', 'dispatchModelOptions', 'dispatchTypeOptions'],
    components: { AntModal, BarAndLineMixChart, ResultTable },
    data() {
      return {
        moment,
        waterFcstOptions: [],
        loading: false,
        modalLoading: false,
        open: false,
        modalTitle: '详情',
        resultData: null,
        chartData: [],
      }
    },
    created() {
      getInWaterPage({ pageNum: 1, pageSize: Number.MAX_SAFE_INTEGER }).then(res => {
        this.waterFcstOptions = (res.data?.data || []).map(el => ({ ...el, label: el.caseName, value: el.inWaterId }))
      })
    },
    computed: {},
    watch: {},
    methods: {
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },
      handleShow(row) {
        this.open = true
        this.modalLoading = true
        this.modalTitle = row.caseName + '模拟结果'

        getDispRes({ resvrDispId: row.resvrDispId }).then(res => {
          this.modalLoading = false
          this.resultData = res.data

          // 处理图表数据
          const data = res.data?.resvrDispResList || []

          const rainData = {
            name: '时段雨量',
            data: data.map(el => [el.tm, el.rain]),
          }
          const sumRainData = {
            name: '累计降雨量',
            data: rainData.data.map((el, idx) => {
              const sum = rainData.data.slice(0, idx + 1).reduce((a, b) => a + b[1], 0)
              return [el[0], +sum.toFixed(1)]
            }),
          }
          this.chartData = [
            rainData,
            sumRainData,
            {
              name: '水位',
              data: data.map(el => [el.tm, el.wlv]),
            },
            {
              name: '入库流量',
              data: data.map(el => [el.tm, el.inflow]),
            },
            {
              name: '供水流量',
              data: data.map(el => [el.tm, el.outflow]),
            },
            {
              name: '泄洪流量',
              data: data.map(el => [el.tm, el.floodflow]),
            }
          ]
        })
      },
      // 计算累计供水量（这里假设供水量等于出库水量减去泄洪水量，具体计算逻辑可根据业务需求调整）
      calculateSupplyWater() {
        if (!this.resultData) return 0
        const sumOutWater = this.resultData.sumOutWater || 0
        const sumFloodWater = this.resultData.sumFloodWater || 0
        return Math.max(0, sumOutWater - sumFloodWater).toFixed(2)
      },
      // 计算洪峰流量
      calculatePeakFlow() {
        if (!this.resultData?.resvrDispResList?.length) return 0
        const flows = this.resultData.resvrDispResList.map(item => item.floodflow || 0)
        return Math.max(...flows).toFixed(2)
      },
      // 计算峰现时间
      calculatePeakTime() {
        if (!this.resultData?.resvrDispResList?.length) return '-'
        const flows = this.resultData.resvrDispResList.map(item => ({
          flow: item.floodflow || 0,
          time: item.tm
        }))
        const maxFlow = Math.max(...flows.map(item => item.flow))
        const peakItem = flows.find(item => item.flow === maxFlow)
        return peakItem ? peakItem.time : '-'
      },
    },
  }
</script>

<style lang="less" scoped>
  ::v-deep .modal-content {
    height: 100%;
  }
  .left {
    width: 340px;
    padding: 14px;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    .item {
      display: flex;
      height: 50px;
      line-height: 30px;
      justify-content: flex-start;
      .label {
        // width: 125px;
        color: #4e5969;
        text-align: left;
      }
      .value {
        flex: 1;
        color: #1d2129;
        // 文字溢出隐藏
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
</style>
